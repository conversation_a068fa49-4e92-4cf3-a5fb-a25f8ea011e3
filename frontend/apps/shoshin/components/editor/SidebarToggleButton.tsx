"use client"

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON>Trigger } from "@/components/ui/tooltip"
import { useSidebarStore } from "@/stores/sidebarStore"
import { PanelRight } from "lucide-react"

export function SidebarToggleButton() {
  const { isCollapsed, expand } = useSidebarStore()

  // Only show when sidebar is collapsed
  if (!isCollapsed) {
    return null
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            onClick={expand}
            className="fixed left-[90px] bottom-[18px] z-10 group flex items-center gap-3 rounded-md border bg-card p-3.5 shadow-sm transition-colors cursor-pointer hover:bg-neutral-50 dark:hover:bg-neutral-800/50"
          >
            {/* Icon */}
            <div className="relative flex h-10 w-10 shrink-0 items-center justify-center overflow-hidden rounded-md bg-primary-500">
              <PanelRight className="text-white transition-transform duration-200 group-hover:scale-110 h-[22px] w-[22px]" />
            </div>

            {/* Content */}
            <div className="mb-[-2px] flex flex-col gap-1">
              <h3 className="font-medium leading-none">Blocks & Tools</h3>
              <p className="text-muted-foreground text-sm leading-snug">Open sidebar</p>
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent side="right">Open Blocks & Tools</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
