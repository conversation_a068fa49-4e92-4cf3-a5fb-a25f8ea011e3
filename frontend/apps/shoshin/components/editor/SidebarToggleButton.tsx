"use client"

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>ipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useSidebarStore } from "@/stores/sidebarStore"
import { PanelRight } from "lucide-react"

export function SidebarToggleButton() {
  const { isCollapsed, expand } = useSidebarStore()

  // Only show when sidebar is collapsed
  if (!isCollapsed) {
    return null
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            onClick={expand}
            className="fixed left-[90px] bottom-[18px] z-10 flex h-8 w-8 items-center justify-center rounded-md cursor-pointer text-muted-foreground hover:bg-accent/50 transition-colors"
          >
            <PanelRight className="h-4 w-4" />
          </div>
        </TooltipTrigger>
        <TooltipContent side="right">Open Blocks & Tools</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
