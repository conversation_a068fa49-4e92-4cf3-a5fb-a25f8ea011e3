"use client"

import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useSidebarStore } from "@/stores/sidebarStore"
import { Package } from "lucide-react"

export function SidebarToggleButton() {
  const { isCollapsed, expand } = useSidebarStore()

  // Only show when sidebar is collapsed
  if (!isCollapsed) {
    return null
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            onClick={expand}
            className="fixed left-[90px] top-1/2 -translate-y-1/2 z-10 flex h-10 w-10 items-center justify-center rounded-md text-muted-foreground hover:bg-accent/50 hover:text-foreground"
          >
            <Package className="h-5 w-5" />
            <span className="sr-only">Open Blocks & Tools</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent side="right">Open Blocks & Tools</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
