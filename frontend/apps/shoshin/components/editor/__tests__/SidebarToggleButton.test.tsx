import { render, screen, fireEvent } from '@testing-library/react'
import { SidebarToggleButton } from '../SidebarToggleButton'
import { useSidebarStore } from '@/stores/sidebarStore'

// Mock the sidebar store
jest.mock('@/stores/sidebarStore')
const mockUseSidebarStore = useSidebarStore as jest.MockedFunction<typeof useSidebarStore>

describe('SidebarToggleButton', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders nothing when sidebar is expanded', () => {
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: false,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: jest.fn(),
    })

    const { container } = render(<SidebarToggleButton />)
    expect(container.firstChild).toBeNull()
  })

  it('renders toggle button when sidebar is collapsed', () => {
    const mockExpand = jest.fn()
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: true,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: mockExpand,
    })

    render(<SidebarToggleButton />)
    
    // Check that button is rendered
    const button = screen.getByRole('button', { name: /open blocks & tools/i })
    expect(button).toBeInTheDocument()
    
    // Check that Package icon is rendered
    expect(button.querySelector('svg')).toBeInTheDocument()
  })

  it('calls expand when button is clicked', () => {
    const mockExpand = jest.fn()
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: true,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: mockExpand,
    })

    render(<SidebarToggleButton />)
    
    const button = screen.getByRole('button', { name: /open blocks & tools/i })
    fireEvent.click(button)
    
    expect(mockExpand).toHaveBeenCalledTimes(1)
  })

  it('has correct positioning classes', () => {
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: true,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: jest.fn(),
    })

    render(<SidebarToggleButton />)
    
    const button = screen.getByRole('button', { name: /open blocks & tools/i })
    expect(button).toHaveClass('fixed', 'left-[90px]', 'top-1/2', '-translate-y-1/2')
  })
})
