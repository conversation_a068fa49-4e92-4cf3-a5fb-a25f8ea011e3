import { useSidebarStore } from '@/stores/sidebarStore'
import { fireEvent, render, screen } from '@testing-library/react'
import { SidebarToggleButton } from '../SidebarToggleButton'

// Mock the sidebar store
jest.mock('@/stores/sidebarStore')
const mockUseSidebarStore = useSidebarStore as jest.MockedFunction<typeof useSidebarStore>

describe('SidebarToggleButton', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders nothing when sidebar is expanded', () => {
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: false,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: jest.fn(),
    })

    const { container } = render(<SidebarToggleButton />)
    expect(container.firstChild).toBeNull()
  })

  it('renders toggle button when sidebar is collapsed', () => {
    const mockExpand = jest.fn()
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: true,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: mockExpand,
    })

    render(<SidebarToggleButton />)

    // Check that toggle element is rendered
    expect(screen.getByText('Blocks & Tools')).toBeInTheDocument()
    expect(screen.getByText('Open sidebar')).toBeInTheDocument()

    // Check that PanelRight icon is rendered
    const toggleElement = screen.getByText('Blocks & Tools').closest('div')
    expect(toggleElement?.querySelector('svg')).toBeInTheDocument()
  })

  it('calls expand when clicked', () => {
    const mockExpand = jest.fn()
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: true,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: mockExpand,
    })

    render(<SidebarToggleButton />)

    const toggleElement = screen.getByText('Blocks & Tools').closest('div')
    fireEvent.click(toggleElement!)

    expect(mockExpand).toHaveBeenCalledTimes(1)
  })

  it('has correct positioning and styling classes', () => {
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: true,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: jest.fn(),
    })

    render(<SidebarToggleButton />)

    const toggleElement = screen.getByText('Blocks & Tools').closest('div')
    expect(toggleElement).toHaveClass('fixed', 'left-[90px]', 'bottom-[18px]')
    expect(toggleElement).toHaveClass('rounded-md', 'border', 'bg-card', 'shadow-sm')
    expect(toggleElement).toHaveClass('hover:bg-neutral-50', 'dark:hover:bg-neutral-800/50')
  })
})
